<!DOCTYPE html>
<html>
  <head>
    <base target="_top">
    <style>
      /* CSS Chung */
      body {
        font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
        line-height: 1.6;
        background-color: #f5f5f5;
        color: #333;
        padding: 20px;
        margin: 0;
      }

      /* Container chính */
      .container {
        max-width: 650px;
        margin: 0 auto;
        background: #fff;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      /* Heading */
      h2 {
        color: #2c3e50;
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #3498db;
      }

      /* Form */
      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #2c3e50;
      }

      .form-control {
        width: 100%;
        padding: 10px 12px;
        font-size: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
        transition: border-color 0.3s;
      }

      .form-control:focus {
        border-color: #3498db;
        outline: none;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      }

      .form-control[readonly] {
        background-color: #f9f9f9;
        cursor: not-allowed;
      }

      /* Button */
      .btn-submit {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        width: 100%;
        transition: background-color 0.3s;
      }

      .btn-submit:hover {
        background-color: #2980b9;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .container {
          padding: 20px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>Tạo Mới Dự Án</h2>
      <form id="form">
        <div class="form-group">
          <label for="ngayTao">Ngày tạo:</label>
          <input type="date" class="form-control" name="ngayTao" id="ngayTao">
        </div>

        <div class="form-group">
          <label for="loaiDuAn">Loại dự án:</label>
          <select class="form-control" name="loaiDuAn" id="loaiDuAn" onchange="generateCode(this.value)">
            <option value="">-- Chọn loại --</option>
            <option value="THI CÔNG, LẮP ĐẶT">THI CÔNG, LẮP ĐẶT</option>
            <option value="XÂY DỰNG TÀI LIỆU">XÂY DỰNG TÀI LIỆU</option>
            <option value="BẢO TRÌ, SỬA CHỮA">BẢO TRÌ, SỬA CHỮA</option>
            <option value="CẢI TIẾN, NÂNG CẤP">CẢI TIẾN, NÂNG CẤP</option>
          </select>
        </div>

        <div class="form-group">
          <label for="maDuAn">Mã dự án:</label>
          <input type="text" class="form-control" name="maDuAn" id="maDuAn" readonly>
        </div>

        <div class="form-group">
          <label for="tenDuAn">Tên dự án:</label>
          <input type="text" class="form-control" name="tenDuAn" id="tenDuAn">
        </div>

        <div class="form-group">
          <label for="mucDich">Mục đích:</label>
          <input type="text" class="form-control" name="mucDich" id="mucDich">
        </div>

        <div class="form-group">
          <label for="loiIch">Lợi ích:</label>
          <input type="text" class="form-control" name="loiIch" id="loiIch">
        </div>

        <div class="form-group">
          <label for="boPhan">Bộ phận thực hiện:</label>
          <select class="form-control" name="boPhan" id="boPhan">
            <option value="">-- Chọn bộ phận --</option>
            <option value="Bộ Phận Tổng Hợp">Bộ Phận Tổng Hợp</option>
            <option value="Bộ Phận Kinh Doanh">Bộ Phận Kinh Doanh</option>
            <option value="Bộ Phận Sản Xuất">Bộ Phận Sản Xuất</option>
            <option value="Bộ Phận Chất Lượng">Bộ Phận Chất Lượng</option>
            <option value="Bộ Phận Kỹ Thuật">Bộ Phận Kỹ Thuật</option>
          </select>
        </div>

        <div class="form-group">
          <label for="mucDoUuTien">Mức độ ưu tiên:</label>
          <select class="form-control" name="mucDoUuTien" id="mucDoUuTien">
            <option value="">-- Chọn mức độ ưu tiên --</option>
            <option value="Cao">Cao</option>
            <option value="Vừa">Vừa</option>
            <option value="Thấp">Thấp</option>
          </select>
        </div>

        <div class="form-group">
          <label for="thoiGian">Thời gian thực hiện:</label>
          <input type="date" class="form-control" name="thoiGian" id="thoiGian">
        </div>

        <div class="form-group">
          <label for="nguoiTao">Người tạo:</label>
          <input type="text" class="form-control" name="nguoiTao" id="nguoiTao" readonly>
        </div>

        <button type="button" class="btn-submit" onclick="submitForm()">Tạo dự án</button>
      </form>
    </div>

    <script>
      // Khi trang được tải, tự động điền ngày hiện tại và lấy thông tin người dùng
      document.addEventListener('DOMContentLoaded', function() {
        // Đặt ngày tạo là ngày hiện tại
        var today = new Date();
        var formattedDate = today.toISOString().substr(0, 10);
        document.getElementById("ngayTao").value = formattedDate;

        // Lấy thông tin người dùng hiện tại và hiển thị họ tên
        google.script.run.withSuccessHandler(function(userInfo) {
          // Hiển thị họ tên trong trường người tạo
          document.getElementById("nguoiTao").value = userInfo.hoTen;
          // Lưu email vào một trường ẩn để gửi lên server
          if (!document.getElementById("emailNguoiTao")) {
            var hiddenField = document.createElement("input");
            hiddenField.type = "hidden";
            hiddenField.id = "emailNguoiTao";
            hiddenField.name = "emailNguoiTao";
            hiddenField.value = userInfo.email;
            document.getElementById("form").appendChild(hiddenField);
          } else {
            document.getElementById("emailNguoiTao").value = userInfo.email;
          }
        }).getCurrentUserInfo();
      });

      function generateCode(loaiDuAn) {
        google.script.run.withSuccessHandler(function(maDuAn) {
          document.getElementById("maDuAn").value = maDuAn;
        }).generateNextProjectCode(loaiDuAn);
      }

      function submitForm() {
        var form = document.getElementById("form");
        var formData = Object.fromEntries(new FormData(form).entries());
        google.script.run.withSuccessHandler(function(msg) {
          alert(msg);
          google.script.host.close();
        }).themDuAnMoi(formData);
      }
    </script>
  </body>
</html>