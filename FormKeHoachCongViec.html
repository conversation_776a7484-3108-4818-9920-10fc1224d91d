<!DOCTYPE html>
<html>
  <head>
    <base target="_top">
    <style>
      /* CSS Chung */
      body {
        font-family: '<PERSON><PERSON>', Arial, sans-serif;
        line-height: 1.6;
        background-color: #f5f5f5;
        color: #333;
        padding: 20px;
        margin: 0;
      }

      /* Container chính */
      .container {
        max-width: 650px;
        margin: 0 auto;
        background: #fff;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      /* Heading */
      h2 {
        color: #2c3e50;
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #3498db;
      }

      /* Form */
      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #2c3e50;
      }

      .form-control {
        width: 100%;
        padding: 10px 12px;
        font-size: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
        transition: border-color 0.3s;
      }

      .form-control:focus {
        border-color: #3498db;
        outline: none;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      }

      textarea.form-control {
        min-height: 100px;
        resize: vertical;
      }

      /* Button */
      .btn-submit {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        width: 100%;
        transition: background-color 0.3s;
      }

      .btn-submit:hover {
        background-color: #2980b9;
      }

      /* User info */
      .user-info {
        display: flex;
        align-items: center;
        margin-bottom: 25px;
        padding: 10px;
        background-color: #e8f4fc;
        border-radius: 6px;
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 15px;
        background-color: #3498db;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
      }

      .user-details {
        flex-grow: 1;
      }

      .user-name {
        font-weight: 500;
        color: #2c3e50;
      }

      .user-email {
        font-size: 12px;
        color: #7f8c8d;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>Nhập Kế Hoạch Công Việc</h2>
      
      <div class="user-info">
        <div class="user-avatar" id="userInitials">?</div>
        <div class="user-details">
          <div class="user-name" id="userName">Đang tải...</div>
          <div class="user-email" id="userEmail">Đang tải...</div>
        </div>
      </div>
      
      <form id="form">
        <div class="form-group">
          <label for="ngayThang">Ngày tháng:</label>
          <input type="date" class="form-control" name="ngayThang" id="ngayThang" required>
        </div>
        
        <div class="form-group">
          <label for="hangMuc">Hạng mục công việc:</label>
          <input type="text" class="form-control" name="hangMuc" id="hangMuc" placeholder="Nhập tên dự án hoặc nhiệm vụ" required>
        </div>
        
        <div class="form-group">
          <label for="noiDung">Nội dung công việc:</label>
          <textarea class="form-control" name="noiDung" id="noiDung" placeholder="Mô tả chi tiết công việc" required></textarea>
        </div>
        
        <button type="button" class="btn-submit" onclick="submitForm()">Lưu kế hoạch</button>
      </form>
    </div>
    
    <script>
      // Khi trang được tải, tự động điền ngày hiện tại và lấy thông tin người dùng
      document.addEventListener('DOMContentLoaded', function() {
        // Đặt ngày tạo là ngày hiện tại
        var today = new Date();
        var formattedDate = today.toISOString().substr(0, 10);
        document.getElementById("ngayThang").value = formattedDate;
        
        // Lấy thông tin người dùng hiện tại và hiển thị họ tên
        google.script.run.withSuccessHandler(setUserInfo).getCurrentUserInfo();
      });
      
      // Thiết lập thông tin người dùng
      function setUserInfo(userInfo) {
        var userName = userInfo.hoTen || userInfo.email.split('@')[0];
        var userEmail = userInfo.email;
        
        document.getElementById('userName').textContent = userName;
        document.getElementById('userEmail').textContent = userEmail;
        
        // Tạo chữ cái đầu cho avatar
        var initials = '';
        if (userName) {
          var nameParts = userName.split(' ');
          if (nameParts.length > 1) {
            initials = nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0);
          } else {
            initials = nameParts[0].charAt(0);
          }
        } else {
          initials = userEmail.charAt(0).toUpperCase();
        }
        document.getElementById('userInitials').textContent = initials.toUpperCase();
      }
      
      function submitForm() {
        var form = document.getElementById("form");
        
        // Kiểm tra form hợp lệ
        if (!form.checkValidity()) {
          alert("Vui lòng điền đầy đủ thông tin!");
          return;
        }
        
        var formData = Object.fromEntries(new FormData(form).entries());
        
        // Gửi dữ liệu lên server
        google.script.run
          .withSuccessHandler(function(msg) {
            alert(msg);
            // Đặt lại form
            document.getElementById("hangMuc").value = "";
            document.getElementById("noiDung").value = "";
            // Không reset ngày tháng
          })
          .withFailureHandler(function(error) {
            alert("Lỗi: " + error);
          })
          .themKeHoachCongViec(formData);
      }
    </script>
  </body>
</html>
