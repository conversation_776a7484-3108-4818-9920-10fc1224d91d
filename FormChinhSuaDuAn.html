<!DOCTYPE html>
<html>
  <head>
    <base target="_top">
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      form {
        max-width: 500px;
        margin: 0 auto;
      }
      label {
        display: block;
        margin-top: 10px;
        font-weight: bold;
      }
      input, select {
        width: 100%;
        padding: 8px;
        margin-top: 5px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
      }
      button {
        background-color: #4285f4;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
      }
      button:hover {
        background-color: #3367d6;
      }
      h3 {
        text-align: center;
        color: #333;
      }
    </style>
  </head>
  <body>
    <h3>Chỉnh sửa dự án: <?= duAn.maDuAn ?></h3>
    <form id="form">
      <label for="ngayTao">Ngày tạo:</label>
      <input type="date" id="ngayTao" name="ngayTao" value="<?= duAn.ngayTao ?>">

      <label for="tenDuAn">Tên dự án:</label>
      <input type="text" id="tenDuAn" name="tenDuAn" value="<?= duAn.tenDuAn ?>">

      <label for="loaiDuAn">Loại dự án:</label>
      <input type="text" id="loaiDuAn" name="loaiDuAn" value="<?= duAn.loaiDuAn ?>">

      <label for="mucDich">Mục đích:</label>
      <input type="text" id="mucDich" name="mucDich" value="<?= duAn.mucDich ?>">

      <label for="loiIch">Lợi ích:</label>
      <input type="text" id="loiIch" name="loiIch" value="<?= duAn.loiIch ?>">

      <label for="boPhan">Bộ phận thực hiện:</label>
      <select id="boPhan" name="boPhan">
        <option value="">-- Chọn bộ phận --</option>
        <option value="Bộ Phận Tổng Hợp" <? if(duAn.boPhan == 'Bộ Phận Tổng Hợp') { ?>selected<? } ?>>Bộ Phận Tổng Hợp</option>
        <option value="Bộ Phận Kinh Doanh" <? if(duAn.boPhan == 'Bộ Phận Kinh Doanh') { ?>selected<? } ?>>Bộ Phận Kinh Doanh</option>
        <option value="Bộ Phận Sản Xuất" <? if(duAn.boPhan == 'Bộ Phận Sản Xuất') { ?>selected<? } ?>>Bộ Phận Sản Xuất</option>
        <option value="Bộ Phận Chất Lượng" <? if(duAn.boPhan == 'Bộ Phận Chất Lượng') { ?>selected<? } ?>>Bộ Phận Chất Lượng</option>
        <option value="Bộ Phận Kỹ Thuật" <? if(duAn.boPhan == 'Bộ Phận Kỹ Thuật') { ?>selected<? } ?>>Bộ Phận Kỹ Thuật</option>
      </select>

      <label for="mucDoUuTien">Mức độ ưu tiên:</label>
      <select id="mucDoUuTien" name="mucDoUuTien">
        <option value="">-- Chọn mức độ ưu tiên --</option>
        <option value="Cao" <? if(duAn.mucDoUuTien == 'Cao') { ?>selected<? } ?>>Cao</option>
        <option value="Vừa" <? if(duAn.mucDoUuTien == 'Vừa') { ?>selected<? } ?>>Vừa</option>
        <option value="Thấp" <? if(duAn.mucDoUuTien == 'Thấp') { ?>selected<? } ?>>Thấp</option>
      </select>

      <label for="thoiGian">Thời gian thực hiện:</label>
      <input type="date" id="thoiGian" name="thoiGian" value="<?= duAn.thoiGian ?>">

      <label for="nguoiTao">Người tạo:</label>
      <input type="text" id="nguoiTao" name="nguoiTao" value="<?= duAn.nguoiTao ?>" readonly>

      <input type="hidden" name="emailNguoiTao" value="<?= duAn.emailNguoiTao ?>">

      <button type="button" onclick="capNhat()">Cập nhật dự án</button>
    </form>

    <script>
      function capNhat() {
        var form = document.getElementById("form");
        var formData = Object.fromEntries(new FormData(form).entries());
        google.script.run.withSuccessHandler(function(msg) {
          alert(msg);
          google.script.host.close();
        }).capNhatDuAn(formData, <?= row ?>);
      }
    </script>
  </body>
</html>
