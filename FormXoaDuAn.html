<!DOCTYPE html>
<html>
  <body>
    <h3><PERSON><PERSON><PERSON> D<PERSON>n</h3>
    <select id="maDuAn">
      <option value="">-- Chọn mã dự án --</option>
    </select><br><br>
    <button onclick="xoa()">Xóa</button>
    <script>
      google.script.run.withSuccessHandler(function(data) {
        var select = document.getElementById("maDuAn");
        data.forEach(function(item) {
          var opt = document.createElement("option");
          opt.value = item.ma;
          opt.text = item.ma + " - " + item.ten;
          select.add(opt);
        });
      }).getDanhSachMaDuAn();

      function xoa() {
        var ma = document.getElementById("maDuAn").value;
        if (ma && confirm("Bạn có chắc muốn xóa dự án " + ma + " không?")) {
          google.script.run.withSuccessHandler(function(msg) {
            alert(msg);
            google.script.host.close();
          }).xoaDuAn(ma);
        }
      }
    </script>
  </body>
</html>
