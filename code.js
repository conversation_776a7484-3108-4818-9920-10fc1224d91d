// Code tạo UI menu và popup form tạo mới dự án
function onOpen() {
  var ui = SpreadsheetApp.getUi();

  ui.createMenu('Quản Lý Dự Án')
    .addItem('Tạo Mới Dự Án', 'showNewProjectForm')
    .addItem('Chỉnh Sửa Dự Án', 'showEditProjectForm')
    .addItem('Xóa Dự Án', 'showDeleteProjectForm')
    .addItem('Duyệt Dự Án', 'showApproveProjectForm')
    .addItem('Cập Nhật STT', 'capNhatSTT')
    .addItem('Sắp Xếp Dự Án', 'sapXepDuAn')
    .addToUi();

  // Cập nhật STT cho Sheet1 khi mở bảng tính
  capNhatSTT();


  // Thêm menu kế hoạch công việc
  ui.createMenu('Kế Hoạch Công Việc')
    .addItem('Mở Sheet Kế Hoạch Của Tôi', 'moSheetKeHoachCongViec')
    .addItem('Thêm Kế Hoạch Công Việc Mới', 'showKeHoachCongViecForm')
    .addSeparator()
    .addItem('Tạo Sheet Kế Hoạch Cho Tất Cả Nhân Viên (Admin)', 'taoSheetKeHoachCongViecChoNhanVien')
    .addToUi();

  // Quản lý quyền xem sheet khi mở bảng tính
  quanLyQuyenXemSheet();
}

// Hàm quản lý quyền xem sheet khi mở bảng tính
function quanLyQuyenXemSheet() {
try {
  // Lấy thông tin người dùng hiện tại
  var userInfo = getCurrentUserInfo();
  var email = userInfo.email;
  var hoTen = userInfo.hoTen;

  // Kiểm tra xem người dùng có phải admin không
  var isAdmin = kiemTraQuyenAdmin(email);

  // Nếu là admin, hiển thị tất cả sheet
  if (isAdmin) {
    return; // Admin có thể thấy tất cả sheet, không cần ẩn gì
  }

  // Tạo tên sheet của người dùng theo định dạng mới (X_Y_Z_Ten)
  var userSheetName = taoTenSheetTuHoTen(hoTen);

  // Lấy danh sách tất cả sheet
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var allSheets = ss.getSheets();

  // Ẩn tất cả sheet kế hoạch công việc trừ sheet của người dùng
  for (var i = 0; i < allSheets.length; i++) {
    var sheet = allSheets[i];
    var sheetName = sheet.getName();

    // Kiểm tra xem có phải sheet kế hoạch công việc không (kiểm tra theo mẫu X_Y_Z_Ten)
    if ((sheetName.match(/^[A-Z](_[A-Z])+_[A-Za-z]+$/) || sheetName.startsWith("KH_")) && sheetName !== userSheetName) {
      sheet.hideSheet();
    }
  }

  // Hiển thị sheet của người dùng nếu có
  var userSheet = ss.getSheetByName(userSheetName);
  if (userSheet && userSheet.isSheetHidden()) {
    userSheet.showSheet();
  }

  Logger.log("Đã quản lý quyền xem sheet cho người dùng: " + hoTen);
} catch (error) {
  Logger.log("Lỗi khi quản lý quyền xem sheet: " + error);
}
}

// Hiển thị form duyệt dự án
function showApproveProjectForm() {
  var html = HtmlService.createHtmlOutputFromFile('FormDuyetDuAn')
    .setWidth(500)
    .setHeight(500)
    .setTitle('Duyệt Dự Án');
  SpreadsheetApp.getUi().showModalDialog(html, 'Duyệt Dự Án');
}

// Hàm lấy thông tin người dùng hiện tại - Lấy email và tìm họ tên từ Sheet2
function getCurrentUserInfo() {
var userEmail = Session.getActiveUser().getEmail();
Logger.log("Email người dùng hiện tại: " + userEmail);

// Kiểm tra xem email có giá trị không
if (!userEmail || userEmail.trim() === "") {
  Logger.log("CẢNH BÁO: Email người dùng trống hoặc không xác định!");
  userEmail = "<EMAIL>"; // Email mặc định nếu không lấy được
  Logger.log("Sử dụng email mặc định: " + userEmail);
}

// Tìm họ tên từ Sheet2 dựa trên email
var hoTen = timHoTenTuEmail(userEmail);

return {
  email: userEmail,
  hoTen: hoTen
};
}

// Hàm tìm họ tên từ email trong Sheet2
function timHoTenTuEmail(email) {
try {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet2 = ss.getSheetByName("Sheet2");

  if (!sheet2) {
    Logger.log("CẢNH BÁO: Không tìm thấy Sheet2!");
    return email; // Trả về email nếu không tìm thấy Sheet2
  }

  var data = sheet2.getDataRange().getValues();

  // Tìm kiếm email trong cột A (index 0)
  for (var i = 1; i < data.length; i++) { // Bắt đầu từ dòng 2 (sau header)
    if (data[i][0] === email) {
      // Nếu tìm thấy, trả về họ tên từ cột B (index 1)
      return data[i][1];
    }
  }

  // Nếu không tìm thấy, trả về email
  Logger.log("Không tìm thấy họ tên cho email: " + email);
  return email;
} catch (error) {
  Logger.log("Lỗi khi tìm họ tên: " + error);
  return email; // Trả về email trong trường hợp lỗi
}
}

// Hàm kiểm tra quyền admin từ email trong Sheet2
function kiemTraQuyenAdmin(email) {
try {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet2 = ss.getSheetByName("Sheet2");

  if (!sheet2) {
    Logger.log("CẢNH BÁO: Không tìm thấy Sheet2!");
    return false; // Trả về false nếu không tìm thấy Sheet2
  }

  var data = sheet2.getDataRange().getValues();

  // Tìm kiếm email trong cột A (index 0)
  for (var i = 1; i < data.length; i++) { // Bắt đầu từ dòng 2 (sau header)
    if (data[i][0] === email) {
      // Kiểm tra quyền ở cột C (index 2)
      if (data[i][2] && data[i][2].toString().toLowerCase() === "admin") {
        return true; // Người dùng có quyền admin
      }
      return false; // Người dùng không có quyền admin
    }
  }

  // Nếu không tìm thấy email, trả về false
  Logger.log("Không tìm thấy thông tin quyền cho email: " + email);
  return false;
} catch (error) {
  Logger.log("Lỗi khi kiểm tra quyền admin: " + error);
  return false; // Trả về false trong trường hợp lỗi
}
}

// Hàm lấy danh sách người dùng có quyền admin
function layDanhSachNguoiDungAdmin() {
try {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet2 = ss.getSheetByName("Sheet2");

  if (!sheet2) {
    Logger.log("CẢNH BÁO: Không tìm thấy Sheet2!");
    return []; // Trả về mảng rỗng nếu không tìm thấy Sheet2
  }

  var data = sheet2.getDataRange().getValues();
  var adminUsers = [];

  // Tìm tất cả người dùng có quyền admin
  for (var i = 1; i < data.length; i++) { // Bắt đầu từ dòng 2 (sau header)
    if (data[i][2] && data[i][2].toString().toLowerCase() === "admin") {
      adminUsers.push(data[i][0]); // Thêm email vào danh sách
    }
  }

  return adminUsers;
} catch (error) {
  Logger.log("Lỗi khi lấy danh sách người dùng admin: " + error);
  return []; // Trả về mảng rỗng trong trường hợp lỗi
}
}

// Hàm duyệt dự án và cập nhật thông tin người duyệt và ngày duyệt
function duyetDuAn(approvalData) {
  var result = timDuAnTheoMa(approvalData.maDuAn);

  if (!result) {
    return "Không tìm thấy dự án với mã " + approvalData.maDuAn;
  }

  // Kiểm tra là duyệt lần 1 hay lần 2
  if (approvalData.lanDuyet === 2) {
    return duyetDuAnLan2(result, approvalData);
  }

  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();

  // Lấy số cột S và T (người duyệt và ngày duyệt lần 1)
  var colNguoiDuyet = 20; // Cột T - 20
  var colNgayDuyet = 21;  // Cột U - 21

  // Lấy thông tin người dùng hiện tại
  var currentUser = getCurrentUserInfo();

  // Cập nhật thông tin duyệt - lưu họ tên người duyệt thay vì email
  sheet.getRange(result.row, colNguoiDuyet).setValue(currentUser.hoTen);

  // Chuyển đổi chuỗi ngày thành đối tượng Date
  var dateParts = approvalData.ngayDuyet.split('-');
  var dateObject = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
  sheet.getRange(result.row, colNgayDuyet).setValue(dateObject);

  // Khóa các ô từ cột A đến K của dự án đã duyệt để không thể chỉnh sửa
  khoaDuAnDaDuyet(result.row);

  // Ghi log hoạt động - vẫn lưu email trong log để dễ theo dõi
  logApprovalActivity(approvalData.maDuAn, currentUser.email, 1);

  return "Đã duyệt lần 1 dự án " + approvalData.maDuAn + " thành công!";
}

// Hàm duyệt dự án lần 2
function duyetDuAnLan2(result, approvalData) {
  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();

  // Kiểm tra xem đã duyệt lần 1 chưa
  var colNguoiDuyetLan1 = 20; // Cột T - 20
  var nguoiDuyetLan1 = sheet.getRange(result.row, colNguoiDuyetLan1).getValue();

  if (!nguoiDuyetLan1) {
    return "Dự án phải được duyệt lần 1 trước khi duyệt lần 2!";
  }

  // Lấy số cột Y và Z (người duyệt và ngày duyệt lần 2)
  var colNguoiDuyetLan2 = 26; // Cột Y - 25
  var colNgayDuyetLan2 = 27;  // Cột Z - 26

  // Lấy thông tin người dùng hiện tại
  var currentUser = getCurrentUserInfo();

  // Cập nhật thông tin duyệt lần 2
  sheet.getRange(result.row, colNguoiDuyetLan2).setValue(currentUser.hoTen);

  // Chuyển đổi chuỗi ngày thành đối tượng Date
  var dateParts = approvalData.ngayDuyet.split('-');
  var dateObject = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
  sheet.getRange(result.row, colNgayDuyetLan2).setValue(dateObject);

  // Khóa các cột từ U đến X
  khoaDuAnDuyetLan2(result.row);

  // Ghi log hoạt động
  logApprovalActivity(approvalData.maDuAn, currentUser.email, 2);

  return "Đã duyệt lần 2 dự án " + approvalData.maDuAn + " thành công!";
}

// Hàm khóa các cột từ U đến X sau khi duyệt lần 2
function khoaDuAnDuyetLan2(row) {
  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();

  // Lấy vùng từ cột L đến Z của dòng dự án
  var range = sheet.getRange(row, 12, 1, 15); // Từ cột L (12) đến Z (26)

  // Thiết lập bảo vệ cho vùng này
  var protection = range.protect().setDescription("Dự án đã duyệt lần 2 - Chỉ admin mới có thể chỉnh sửa");

  // Lấy danh sách người dùng có quyền admin
  var adminUsers = layDanhSachNguoiDungAdmin();

  // Xóa tất cả người dùng hiện tại khỏi danh sách editor
  protection.removeEditors(protection.getEditors());

  // Thêm tất cả người dùng admin vào danh sách editor
  if (adminUsers.length > 0) {
    adminUsers.forEach(function(email) {
      try {
        protection.addEditor(email);
        Logger.log("Đã thêm quyền chỉnh sửa cho admin: " + email);
      } catch (error) {
        Logger.log("Lỗi khi thêm quyền cho admin " + email + ": " + error);
      }
    });
  } else {
    // Nếu không có admin nào, thêm người dùng hiện tại làm editor
    var me = Session.getEffectiveUser();
    protection.addEditor(me);
    Logger.log("Không tìm thấy admin nào, thêm người dùng hiện tại: " + me.getEmail());
  }

  // Tắt quyền chỉnh sửa cho domain
  if (protection.canDomainEdit()) {
    protection.setDomainEdit(false);
  }
}

// Hàm khóa các ô từ cột A đến R của dự án đã duyệt
function khoaDuAnDaDuyet(row) {
  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();

  // Lấy vùng từ cột A đến K của dòng dự án
  var range = sheet.getRange(row, 1, 1, 11); // Từ cột A (1) đến K (11)

  // Thiết lập bảo vệ cho vùng này
  var protection = range.protect().setDescription("Dự án đã duyệt - Chỉ admin mới có thể chỉnh sửa");

  // Lấy danh sách người dùng có quyền admin
  var adminUsers = layDanhSachNguoiDungAdmin();

  // Xóa tất cả người dùng hiện tại khỏi danh sách editor
  protection.removeEditors(protection.getEditors());

  // Thêm tất cả người dùng admin vào danh sách editor
  if (adminUsers.length > 0) {
    adminUsers.forEach(function(email) {
      try {
        protection.addEditor(email);
        Logger.log("Đã thêm quyền chỉnh sửa cho admin: " + email);
      } catch (error) {
        Logger.log("Lỗi khi thêm quyền cho admin " + email + ": " + error);
      }
    });
  } else {
    // Nếu không có admin nào, thêm người dùng hiện tại làm editor
    var me = Session.getEffectiveUser();
    protection.addEditor(me);
    Logger.log("Không tìm thấy admin nào, thêm người dùng hiện tại: " + me.getEmail());
  }

  // Tắt quyền chỉnh sửa cho domain
  if (protection.canDomainEdit()) {
    protection.setDomainEdit(false);
  }
}

// Cập nhật hàm ghi log để hỗ trợ ghi log cho cả 2 lần duyệt
function logApprovalActivity(maDuAn, userEmail, lanDuyet) {
  console.log("Dự án " + maDuAn + " được duyệt lần " + lanDuyet + " bởi " + userEmail + " vào " + new Date());
}

// Cập nhật hàm kiểm tra trạng thái duyệt
function kiemTraTrangThaiDuyet(maDuAn) {
  var result = timDuAnTheoMa(maDuAn);

  if (!result) {
    return { approved: false, message: "Không tìm thấy dự án" };
  }

  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  var colNguoiDuyetLan1 = 20; // Cột T - 20
  var colNguoiDuyetLan2 = 26; // Cột Z - 26

  // Kiểm tra xem có người duyệt lần 1 hay chưa
  var nguoiDuyetLan1 = sheet.getRange(result.row, colNguoiDuyetLan1).getValue();
  var nguoiDuyetLan2 = sheet.getRange(result.row, colNguoiDuyetLan2).getValue();

  if (nguoiDuyetLan2 && nguoiDuyetLan2.toString().trim() !== "") {
    return {
      approved: true,
      approver1: nguoiDuyetLan1,
      approver2: nguoiDuyetLan2,
      level: 2
    };
  } else if (nguoiDuyetLan1 && nguoiDuyetLan1.toString().trim() !== "") {
    return {
      approved: true,
      approver1: nguoiDuyetLan1,
      level: 1
    };
  } else {
    return { approved: false, message: "Dự án chưa được duyệt" };
  }
}

// 2. TẠO FORM TÌM KIẾM DỰ ÁN ĐỂ CHỈNH SỬA
function showEditProjectForm() {
  var html = HtmlService.createHtmlOutputFromFile('FormTimKiemDuAn')
    .setWidth(400)
    .setHeight(200)
    .setTitle('Tìm Dự Án Để Chỉnh Sửa');
  SpreadsheetApp.getUi().showModalDialog(html, 'Tìm Dự Án Để Chỉnh Sửa');
}
// 3. TẠO FORM XÓA DỰ ÁN
function showDeleteProjectForm() {
  var html = HtmlService.createHtmlOutputFromFile('FormXoaDuAn')
    .setWidth(400)
    .setHeight(200)
    .setTitle('Xóa Dự Án');
  SpreadsheetApp.getUi().showModalDialog(html, 'Xóa Dự Án');
}
// 4. HÀM TÌM KIẾM DỰ ÁN THEO MÃ
function timDuAnTheoMa(maDuAn) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();

  // Luôn lấy Sheet1 bất kể sheet nào đang active
  var sheet = ss.getSheetByName("Sheet1");

  // Nếu không tìm thấy Sheet1, trả về null
  if (!sheet) {
    Logger.log("Không tìm thấy Sheet1");
    return null;
  }

  var data = sheet.getDataRange().getValues();

  // Tìm kiếm dự án theo mã
  for (var i = 1; i < data.length; i++) {  // Bắt đầu từ dòng 2 (sau header)
    if (data[i][2] === maDuAn) {  // Cột 3 (index 2) chứa mã dự án
      // Lấy email người tạo và người duyệt
      var emailNguoiTao = data[i][10];
      var emailNguoiDuyet = data[i][19] || "";

      // Tìm họ tên tương ứng từ email
      var hoTenNguoiTao = timHoTenTuEmail(emailNguoiTao);
      var hoTenNguoiDuyet = emailNguoiDuyet ? timHoTenTuEmail(emailNguoiDuyet) : "";

      // Lấy thông tin người duyệt lần 2 và ngày duyệt lần 2
      var emailNguoiDuyetLan2 = data[i][25] || "";
      var hoTenNguoiDuyetLan2 = emailNguoiDuyetLan2 ? timHoTenTuEmail(emailNguoiDuyetLan2) : "";

      // Trả về thông tin dự án và vị trí dòng
      return {
        row: i + 1,  // Chuyển từ index sang số dòng thực tế
        data: {
          stt: data[i][0],
          ngayTao: formatDate(data[i][1]),
          maDuAn: data[i][2],
          tenDuAn: data[i][3],
          loaiDuAn: data[i][4],
          mucDich: data[i][5],
          loiIch: data[i][6],
          boPhan: data[i][7],
          mucDoUuTien: data[i][8],
          thoiGian: formatDate(data[i][9]),
          nguoiTao: hoTenNguoiTao,
          emailNguoiTao: emailNguoiTao, // Lưu cả email để sử dụng khi cần
          nguoiDuyet: hoTenNguoiDuyet,
          emailNguoiDuyet: emailNguoiDuyet, // Lưu cả email để sử dụng khi cần
          ngayDuyet: formatDate(data[i][20] || ""), // Cột T (index 20)
          nguoiDuyetLan2: hoTenNguoiDuyetLan2,
          emailNguoiDuyetLan2: emailNguoiDuyetLan2,
          ngayDuyetLan2: formatDate(data[i][26] || "") // Cột Z (index 26)
        }
      };
    }
  }

  // Nếu không tìm thấy
  return null;
}
// Hàm định dạng ngày tháng để hiển thị trong form
function formatDate(date) {
  if (!(date instanceof Date) || isNaN(date)) {
    return '';
  }
  var year = date.getFullYear();
  var month = ('0' + (date.getMonth() + 1)).slice(-2);
  var day = ('0' + date.getDate()).slice(-2);
  return year + '-' + month + '-' + day;
}

// 6. HÀM XÓA DỰ ÁN
function xoaDuAn(maDuAn) {
  var result = timDuAnTheoMa(maDuAn);

  if (!result) {
    return "Không tìm thấy dự án với mã " + maDuAn;
  }

  // Kiểm tra xem dự án đã được duyệt chưa
  var trangThaiDuyet = kiemTraTrangThaiDuyet(maDuAn);

  // Lấy thông tin người dùng hiện tại
  var currentUser = getCurrentUserInfo();
  var userEmail = currentUser.email;

  // Kiểm tra quyền admin
  var isAdmin = kiemTraQuyenAdmin(userEmail);

  // Nếu dự án đã được duyệt và người dùng không phải admin
  if (trangThaiDuyet.approved && !isAdmin) {
    return "Bạn không có quyền xóa dự án đã được duyệt. Chỉ người dùng có quyền admin mới có thể thực hiện thao tác này.";
  }

  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName("Sheet1");

  // Nếu không tìm thấy Sheet1, trả về thông báo lỗi
  if (!sheet) {
    Logger.log("Không tìm thấy Sheet1");
    return "Không tìm thấy Sheet1";
  }

  try {
    // Xóa bảo vệ nếu có
    var protections = sheet.getProtections(SpreadsheetApp.ProtectionType.RANGE);
    for (var i = 0; i < protections.length; i++) {
      var protection = protections[i];
      var protectedRange = protection.getRange();

      // Kiểm tra xem vùng bảo vệ có chứa dòng cần xóa không
      if (protectedRange &&
          protectedRange.getRow() <= result.row &&
          result.row <= protectedRange.getLastRow()) {
        protection.remove();
        Logger.log("Đã xóa bảo vệ cho dòng " + result.row);
      }
    }

    // Xóa dòng
    sheet.deleteRow(result.row);

    // Cập nhật lại số thứ tự cho các dự án còn lại
    capNhatSTT();

    // Ghi log hoạt động
    Logger.log("Dự án " + maDuAn + " đã bị xóa bởi " + userEmail);

    return "Đã xóa dự án " + maDuAn + " thành công!";
  } catch (error) {
    Logger.log("Lỗi khi xóa dự án: " + error);
    return "Có lỗi xảy ra khi xóa dự án: " + error;
  }
}

// 7. HÀM CẬP NHẬT LẠI SỐ THỨ TỰ
function capNhatSTT() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();

  // Luôn lấy Sheet1 bất kể sheet nào đang active
  var sheet = ss.getSheetByName("Sheet1");

  // Nếu không tìm thấy Sheet1, thoát khỏi hàm
  if (!sheet) {
    Logger.log("Không tìm thấy Sheet1");
    return;
  }

  var data = sheet.getDataRange().getValues();
  var count = 0;

  // Bắt đầu từ dòng 2 (sau header)
  for (var i = 1; i < data.length; i++) {
    // Kiểm tra xem dòng có dữ liệu không (kiểm tra cột mã dự án)
    if (data[i][2]) {
      count++;
      sheet.getRange(i + 1, 1).setValue(count);
    }
  }

  Logger.log("Đã cập nhật STT cho Sheet1");
}

// Hàm sắp xếp dự án theo trạng thái duyệt và ngày tạo
function sapXepDuAn() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();

  // Luôn lấy Sheet1 bất kể sheet nào đang active
  var sheet = ss.getSheetByName("Sheet1");

  // Nếu không tìm thấy Sheet1, thoát khỏi hàm
  if (!sheet) {
    Logger.log("Không tìm thấy Sheet1");
    SpreadsheetApp.getUi().alert("Không tìm thấy Sheet1!");
    return;
  }

  var data = sheet.getDataRange().getValues();
  var headerRow = data[0]; // Lưu lại dòng header
  var duAnRows = [];

  // Thu thập tất cả các dòng có dữ liệu dự án và thông tin trạng thái duyệt
  for (var i = 1; i < data.length; i++) {
    // Kiểm tra xem dòng có dữ liệu không (kiểm tra cột mã dự án)
    if (data[i][2]) {
      // Kiểm tra trạng thái duyệt (cột T - người duyệt lần 1)
      var nguoiDuyetLan1 = data[i][19]; // Cột T (index 19)
      var daDuyetLan1 = nguoiDuyetLan1 && nguoiDuyetLan1.toString().trim() !== "";

      duAnRows.push({
        rowData: data[i],
        daDuyetLan1: daDuyetLan1,
        ngayTao: data[i][1] // Lưu ngày tạo để sắp xếp thứ cấp
      });
    }
  }

  // Sắp xếp các dòng theo trạng thái duyệt và ngày tạo
  duAnRows.sort(function(a, b) {
    // Ưu tiên 1: Dự án chưa duyệt lần 1 lên trước
    if (a.daDuyetLan1 !== b.daDuyetLan1) {
      return a.daDuyetLan1 ? 1 : -1; // false lên trước true
    }

    // Ưu tiên 2: Nếu cùng trạng thái duyệt, sắp xếp theo ngày tạo (mới nhất lên trước)
    if (a.ngayTao instanceof Date && b.ngayTao instanceof Date) {
      return b.ngayTao - a.ngayTao; // Sắp xếp giảm dần theo ngày (mới nhất lên trước)
    }

    return 0;
  });

  // Xây dựng lại dữ liệu đã sắp xếp
  var sortedData = [headerRow]; // Bắt đầu với dòng header
  for (var j = 0; j < duAnRows.length; j++) {
    sortedData.push(duAnRows[j].rowData);
  }

  // Xóa tất cả dữ liệu hiện tại
  var lastRow = sheet.getLastRow();
  var lastCol = sheet.getLastColumn();
  if (lastRow > 1) {
    sheet.getRange(1, 1, lastRow, lastCol).clearContent();
  }

  // Ghi dữ liệu đã sắp xếp vào sheet
  sheet.getRange(1, 1, sortedData.length, sortedData[0].length).setValues(sortedData);

  // Cập nhật lại STT
  capNhatSTT();

  // Hiển thị thông báo
  SpreadsheetApp.getUi().alert("Đã sắp xếp dự án thành công! Các dự án chưa duyệt lần 1 được hiển thị lên trước, và dự án mới tạo hiển thị lên trước.");
}

function getDanhSachMaDuAn() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();

  // Luôn lấy Sheet1 bất kể sheet nào đang active
  var sheet = ss.getSheetByName("Sheet1");

  // Nếu không tìm thấy Sheet1, trả về mảng rỗng
  if (!sheet) {
    Logger.log("Không tìm thấy Sheet1");
    return [];
  }

  var data = sheet.getDataRange().getValues();
  var danhSachMa = [];

  // Bắt đầu từ dòng 2 (sau header)
  for (var i = 1; i < data.length; i++) {
    var maDuAn = data[i][2];
    if (maDuAn) {
      danhSachMa.push({
        ma: maDuAn,
        ten: data[i][3]  // Lấy thêm tên dự án để hiển thị
      });
    }
  }

  return danhSachMa;
}

function showEditProjectFormWithData(maDuAn) {
  var duAn = timDuAnTheoMa(maDuAn);

  if (!duAn) {
    SpreadsheetApp.getUi().alert("Không tìm thấy dự án với mã " + maDuAn);
    return;
  }

  // Kiểm tra xem dự án đã được duyệt chưa
  var trangThaiDuyet = kiemTraTrangThaiDuyet(maDuAn);

  // Lấy thông tin người dùng hiện tại
  var currentUser = getCurrentUserInfo();
  var userEmail = currentUser.email;

  // Kiểm tra quyền admin
  var isAdmin = kiemTraQuyenAdmin(userEmail);

  // Nếu dự án đã được duyệt và người dùng không phải admin
  if (trangThaiDuyet.approved && !isAdmin) {
    var nguoiDuyet = trangThaiDuyet.approver1 || "";
    if (trangThaiDuyet.level === 2) {
      nguoiDuyet += " và " + trangThaiDuyet.approver2;
    }
    SpreadsheetApp.getUi().alert("Dự án này đã được duyệt bởi " + nguoiDuyet + " và không thể chỉnh sửa! Chỉ người dùng có quyền admin mới có thể chỉnh sửa dự án đã duyệt.");
    return;
  }

  // Nếu dự án đã được duyệt và người dùng là admin, hiển thị thông báo nhưng vẫn cho phép chỉnh sửa
  if (trangThaiDuyet.approved && isAdmin) {
    var nguoiDuyet = trangThaiDuyet.approver1 || "";
    if (trangThaiDuyet.level === 2) {
      nguoiDuyet += " và " + trangThaiDuyet.approver2;
    }
    SpreadsheetApp.getUi().alert("Dự án này đã được duyệt bởi " + nguoiDuyet + ". Bạn có quyền admin nên có thể chỉnh sửa dự án này.");
  }

  var template = HtmlService.createTemplateFromFile('FormChinhSuaDuAn');
  template.duAn = duAn.data;
  template.row = duAn.row;
  template.isAdmin = isAdmin; // Truyền thông tin quyền admin vào template

  var html = template.evaluate()
    .setWidth(600)
    .setHeight(500)
    .setTitle('Chỉnh Sửa Dự Án');

  SpreadsheetApp.getUi().showModalDialog(html, 'Chỉnh Sửa Dự Án');
}
function capNhatDuAn(formData, row) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();

  // Luôn lấy Sheet1 bất kể sheet nào đang active
  var sheet = ss.getSheetByName("Sheet1");

  // Nếu không tìm thấy Sheet1, trả về thông báo lỗi
  if (!sheet) {
    Logger.log("Không tìm thấy Sheet1");
    return "Không tìm thấy Sheet1";
  }

  // Lấy mã dự án từ dòng hiện tại để kiểm tra trạng thái duyệt
  var maDuAn = sheet.getRange(row, 3).getValue();

  // Kiểm tra xem dự án đã được duyệt chưa
  var trangThaiDuyet = kiemTraTrangThaiDuyet(maDuAn);

  // Lấy thông tin người dùng hiện tại
  var currentUser = getCurrentUserInfo();
  var userEmail = currentUser.email;

  // Kiểm tra quyền admin
  var isAdmin = kiemTraQuyenAdmin(userEmail);

  // Nếu dự án đã được duyệt và người dùng không phải admin
  if (trangThaiDuyet.approved && !isAdmin) {
    return "Bạn không có quyền cập nhật dự án đã được duyệt. Chỉ người dùng có quyền admin mới có thể thực hiện thao tác này.";
  }

  try {
    // Nếu dự án đã được duyệt và người dùng là admin, xóa bảo vệ trước khi cập nhật
    if (trangThaiDuyet.approved && isAdmin) {
      var protections = sheet.getProtections(SpreadsheetApp.ProtectionType.RANGE);
      for (var i = 0; i < protections.length; i++) {
        var protection = protections[i];
        var protectedRange = protection.getRange();

        // Kiểm tra xem vùng bảo vệ có chứa dòng cần cập nhật không
        if (protectedRange &&
            protectedRange.getRow() <= row &&
            row <= protectedRange.getLastRow()) {
          protection.remove();
          Logger.log("Đã xóa bảo vệ cho dòng " + row);
        }
      }
    }

    // Lấy email người tạo từ form data hoặc giữ nguyên nếu không có
    var emailNguoiTao = formData.emailNguoiTao || formData.nguoiTao;

    // Lấy họ tên từ email
    var hoTenNguoiTao = timHoTenTuEmail(emailNguoiTao);

    // Cập nhật dữ liệu trong sheet (không cập nhật mã dự án và STT)
    sheet.getRange(row, 2).setValue(formData.ngayTao);
    // Không cập nhật mã dự án (cột 3)
    sheet.getRange(row, 4).setValue(formData.tenDuAn);
    sheet.getRange(row, 5).setValue(formData.loaiDuAn);
    sheet.getRange(row, 6).setValue(formData.mucDich);
    sheet.getRange(row, 7).setValue(formData.loiIch);
    sheet.getRange(row, 8).setValue(formData.boPhan);
    sheet.getRange(row, 9).setValue(formData.mucDoUuTien);
    sheet.getRange(row, 10).setValue(formData.thoiGian);
    sheet.getRange(row, 11).setValue(hoTenNguoiTao); // Lưu họ tên thay vì email

    // Lưu email vào một cột ẩn hoặc sheet khác nếu cần truy xuất sau này
    // Ví dụ: sheet.getRange(row, 25).setValue(emailNguoiTao); // Cột Y - 25

    // Nếu dự án đã được duyệt và người dùng là admin, thiết lập lại bảo vệ sau khi cập nhật
    if (trangThaiDuyet.approved && isAdmin) {
      khoaDuAnDaDuyet(row);
      Logger.log("Đã thiết lập lại bảo vệ cho dòng " + row);
    }

    // Ghi log hoạt động
    Logger.log("Dự án " + maDuAn + " đã được cập nhật bởi " + userEmail);

    return "Đã cập nhật dự án thành công!";
  } catch (error) {
    Logger.log("Lỗi khi cập nhật dự án: " + error);
    return "Có lỗi xảy ra khi cập nhật dự án: " + error;
  }
}

function showNewProjectForm() {
  var html = HtmlService.createHtmlOutputFromFile('FormTaoMoiDuAn')
    .setWidth(600)
    .setHeight(500)
    .setTitle('Tạo Mới Dự Án');
  SpreadsheetApp.getUi().showModalDialog(html, 'Tạo Mới Dự Án');
}

function generateNextProjectCode(loaiDuAn) {
  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  var data = sheet.getDataRange().getValues();
  var prefix = "";

  // Xác định prefix dựa trên loại dự án
  switch(loaiDuAn) {
    case "THI CÔNG, LẮP ĐẶT":
      prefix = "TCLD";
      break;
    case "XÂY DỰNG TÀI LIỆU":
      prefix = "XDTL";
      break;
    case "BẢO TRÌ, SỬA CHỮA":
      prefix = "BTSC";
      break;
    case "CẢI TIẾN, NÂNG CẤP":
      prefix = "CTNC";
      break;
    default:
      prefix = "KHAC";
  }

  // Đếm tổng số dự án (số thứ tự dự án đã được tạo)
  var totalProjects = 0;
  for (var i = 1; i < data.length; i++) {
    if (data[i][2]) { // Kiểm tra cột mã dự án có dữ liệu
      totalProjects++;
    }
  }

  // Số thứ tự dự án tiếp theo
  var nextTotalNumber = totalProjects + 1;

  // Đếm số dự án của loại này
  var projectsOfType = 0;
  for (var i = 1; i < data.length; i++) {
    var loaiDuAnHienTai = data[i][4] ? data[i][4].toString() : "";

    if (loaiDuAnHienTai === loaiDuAn) {
      projectsOfType++;
    }
  }

  // Số thứ tự dự án của loại này tiếp theo
  var nextTypeNumber = projectsOfType + 1;

  // Định dạng các số với độ dài cố định
  var formattedTotalNumber = nextTotalNumber.toString();
  var formattedTypeNumber = padNumber(nextTypeNumber, 3); // Định dạng số với độ dài 3 (001, 002, ...)

  // Tạo mã dự án theo định dạng: số thứ tự dự án đã được tạo_số thứ tự dự án của từng loại_tên viết tắt của loại dự án
  return formattedTotalNumber + "_" + formattedTypeNumber + "_" + prefix;
}

// Hàm để thêm số 0 phía trước nếu cần
function padNumber(number, length) {
  var str = '' + number;
  while (str.length < length) {
    str = '0' + str;
  }
  return str;
}


function themDuAnMoi(formData) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();

  // Luôn lấy Sheet1 bất kể sheet nào đang active
  var sheet = ss.getSheetByName("Sheet1");

  // Nếu không tìm thấy Sheet1, trả về thông báo lỗi
  if (!sheet) {
    Logger.log("Không tìm thấy Sheet1");
    return "Không tìm thấy Sheet1";
  }

  // Lấy email người tạo từ form data
  var emailNguoiTao = formData.emailNguoiTao || formData.nguoiTao;

  // Lấy họ tên từ email
  var hoTenNguoiTao = timHoTenTuEmail(emailNguoiTao);

  // Tạo một mảng dữ liệu cho dòng mới
  var newRowData = [
    "", // STT (sẽ được cập nhật sau)
    formData.ngayTao,
    formData.maDuAn,
    formData.tenDuAn,
    formData.loaiDuAn,
    formData.mucDich,
    formData.loiIch,
    formData.boPhan,
    formData.mucDoUuTien,
    formData.thoiGian,
    hoTenNguoiTao
  ];

  // Lấy số cột cần thiết
  var numCols = newRowData.length;

  // Thêm dòng mới vào vị trí thứ 2 (sau header) mà không kế thừa định dạng
  // Phương pháp: Chèn dòng trống và sau đó điền dữ liệu
  sheet.insertRowBefore(2);

  // Điền dữ liệu vào dòng mới
  sheet.getRange(2, 1, 1, numCols).setValues([newRowData]);

  // Đảm bảo dòng mới có màu nền mặc định (trắng)
  sheet.getRange(2, 1, 1, sheet.getLastColumn()).setBackground(null);

  // Lưu email vào một cột ẩn hoặc sheet khác nếu cần truy xuất sau này
  // Ví dụ: sheet.getRange(newRow, 25).setValue(emailNguoiTao); // Cột Y - 25

  // Cập nhật lại STT cho tất cả các dự án
  capNhatSTT();

  return "Đã thêm dự án mới thành công!";
}

// Hàm lấy dòng cuối cùng có dữ liệu
function getLastDataRow(sheet) {
  var lastRow = sheet.getLastRow();
  return lastRow;
}



// Hàm tạo sheet kế hoạch công việc cho tất cả nhân viên
function taoSheetKeHoachCongViecChoNhanVien() {
var ss = SpreadsheetApp.getActiveSpreadsheet();
var sheet2 = ss.getSheetByName("Sheet2");

if (!sheet2) {
  Logger.log("CẢNH BÁO: Không tìm thấy Sheet2!");
  SpreadsheetApp.getUi().alert("Không tìm thấy Sheet2 chứa thông tin nhân viên!");
  return;
}

var data = sheet2.getDataRange().getValues();
var countCreated = 0;

// Bắt đầu từ dòng 2 (sau header)
for (var i = 1; i < data.length; i++) {
  var email = data[i][0];
  var hoTen = data[i][1];

  if (email && hoTen) {
    // Tạo tên sheet từ họ tên theo định dạng mới (X_Y_Z_Ten)
    var sheetName = taoTenSheetTuHoTen(hoTen);

    // Kiểm tra xem sheet đã tồn tại chưa
    var existingSheet = ss.getSheetByName(sheetName);
    if (!existingSheet) {
      // Tạo sheet mới
      var newSheet = ss.insertSheet(sheetName);

      // Thiết lập mẫu kế hoạch công việc
      thietLapMauKeHoachCongViec(newSheet, hoTen, email);
      countCreated++;
    }
  }
}

SpreadsheetApp.getUi().alert("Đã tạo " + countCreated + " sheet kế hoạch công việc mới cho nhân viên!");
}

// Hàm thiết lập mẫu kế hoạch công việc
function thietLapMauKeHoachCongViec(sheet, hoTen, email) {
// Thiết lập tiêu đề và logo
sheet.setColumnWidth(1, 120); // Cột ngày/tháng
sheet.setColumnWidth(2, 250); // Cột hạng mục công việc
sheet.setColumnWidth(3, 500); // Cột nội dung công việc

// Thiết lập header
var headerRange = sheet.getRange("A1:C1");
headerRange.merge();
headerRange.setValue("KẾ HOẠCH CÔNG VIỆC");
headerRange.setFontSize(16);
headerRange.setFontWeight("bold");
headerRange.setHorizontalAlignment("center");
headerRange.setBackground("#D9EAD3");

// Thiết lập thông tin nhân viên
var infoRange = sheet.getRange("A2:C2");
infoRange.merge();
infoRange.setValue("Nhân viên: " + hoTen);
infoRange.setFontWeight("bold");
infoRange.setHorizontalAlignment("center");

// Thiết lập tiêu đề cột
sheet.getRange("A3").setValue("Ngày/Tháng");
sheet.getRange("B3").setValue("Hạng mục công việc");
sheet.getRange("C3").setValue("Nội dung công việc");

var headerColRange = sheet.getRange("A3:C3");
headerColRange.setFontWeight("bold");
headerColRange.setBackground("#D9EAD3");
headerColRange.setHorizontalAlignment("center");

// Thêm mô tả và ví dụ
sheet.getRange("A4").setValue(new Date());
sheet.getRange("B4").setValue("Tên dự án hoặc nhiệm vụ trong mô tả công việc hoặc ghi công việc công ty hàng ngày");
sheet.getRange("C4").setValue("Ghi nội dung chi tiết công việc");

// Thêm 20 dòng trống
for (var i = 5; i <= 25; i++) {
  sheet.getRange("A" + i).setValue("");
  sheet.getRange("B" + i).setValue("");
  sheet.getRange("C" + i).setValue("");
}

// Ẩn sheet để người khác không thể nhìn thấy
sheet.hideSheet();

// Thiết lập bảo vệ sheet để chỉ người sở hữu và admin mới có thể chỉnh sửa
var protection = sheet.protect().setDescription("Kế hoạch công việc của " + hoTen);

// Lấy người dùng hiện tại (người đang chạy script)
var me = Session.getEffectiveUser();

// Xóa tất cả người dùng hiện tại khỏi danh sách editor, nhưng giữ lại chủ sở hữu
var editors = protection.getEditors();
protection.removeEditors(editors);

// Đảm bảo người dùng hiện tại luôn có quyền chỉnh sửa
protection.addEditor(me);
Logger.log("Đã thêm quyền chỉnh sửa cho người dùng hiện tại: " + me.getEmail());

// Thêm người dùng sở hữu vào danh sách editor nếu khác với người dùng hiện tại
if (email !== me.getEmail()) {
  try {
    protection.addEditor(email);
    Logger.log("Đã thêm quyền chỉnh sửa cho " + hoTen + " (" + email + ")");
  } catch (error) {
    Logger.log("Lỗi khi thêm quyền cho " + hoTen + " (" + email + "): " + error);
  }
}

// Thêm admin vào danh sách editor
var adminUsers = layDanhSachNguoiDungAdmin();
if (adminUsers.length > 0) {
  adminUsers.forEach(function(adminEmail) {
    if (adminEmail !== me.getEmail()) { // Tránh thêm trùng lặp
      try {
        protection.addEditor(adminEmail);
        Logger.log("Đã thêm quyền chỉnh sửa cho admin: " + adminEmail);
      } catch (error) {
        Logger.log("Lỗi khi thêm quyền cho admin " + adminEmail + ": " + error);
      }
    }
  });
}

// Tắt quyền chỉnh sửa cho domain
if (protection.canDomainEdit()) {
  protection.setDomainEdit(false);
}
}

// Hàm mở sheet kế hoạch công việc của người dùng hiện tại
function moSheetKeHoachCongViec() {
var userInfo = getCurrentUserInfo();
var email = userInfo.email;
var hoTen = userInfo.hoTen;

// Tạo tên sheet từ họ tên theo định dạng mới (X_Y_Z_Ten)
var sheetName = taoTenSheetTuHoTen(hoTen);

var ss = SpreadsheetApp.getActiveSpreadsheet();
var sheet = ss.getSheetByName(sheetName);

if (sheet) {
  // Hiển thị sheet nếu đang bị ẩn
  if (sheet.isSheetHidden()) {
    sheet.showSheet();
  }

  // Đảm bảo người dùng hiện tại có quyền chỉnh sửa sheet
  try {
    var protections = sheet.getProtections(SpreadsheetApp.ProtectionType.SHEET);
    if (protections && protections.length > 0) {
      var protection = protections[0];
      var me = Session.getEffectiveUser();

      // Thêm người dùng hiện tại vào danh sách editor
      protection.addEditor(me);
      Logger.log("Đã thêm quyền chỉnh sửa cho người dùng hiện tại: " + me.getEmail());
    }
  } catch (error) {
    Logger.log("Lỗi khi cập nhật quyền: " + error);
  }

  // Kích hoạt sheet
  sheet.activate();

  // Ẩn tất cả các sheet khác trừ sheet hiện tại và Sheet1, Sheet2
  var allSheets = ss.getSheets();
  for (var i = 0; i < allSheets.length; i++) {
    var currentSheet = allSheets[i];
    var currentSheetName = currentSheet.getName();

    // Không ẩn sheet hiện tại, Sheet1 và Sheet2
    if (currentSheetName !== sheetName &&
        currentSheetName !== "Sheet1" &&
        currentSheetName !== "Sheet2") {

      // Kiểm tra xem người dùng có phải admin không
      var isAdmin = kiemTraQuyenAdmin(email);

      // Nếu không phải admin, ẩn sheet khác
      if (!isAdmin) {
        currentSheet.hideSheet();
      }
    }
  }
} else {
  // Nếu sheet chưa tồn tại, hỏi người dùng có muốn tạo mới không
  var ui = SpreadsheetApp.getUi();
  var response = ui.alert(
    'Sheet kế hoạch công việc chưa tồn tại',
    'Bạn muốn tạo mới sheet kế hoạch công việc không?',
    ui.ButtonSet.YES_NO);

  if (response == ui.Button.YES) {
    // Tạo sheet mới
    var newSheet = ss.insertSheet(sheetName);

    // Thiết lập mẫu kế hoạch công việc
    thietLapMauKeHoachCongViec(newSheet, hoTen, email);

    // Hiển thị sheet
    newSheet.showSheet();

    // Đảm bảo người dùng hiện tại có quyền chỉnh sửa sheet
    try {
      var protections = newSheet.getProtections(SpreadsheetApp.ProtectionType.SHEET);
      if (protections && protections.length > 0) {
        var protection = protections[0];
        var me = Session.getEffectiveUser();

        // Thêm người dùng hiện tại vào danh sách editor
        protection.addEditor(me);
        Logger.log("Đã thêm quyền chỉnh sửa cho người dùng hiện tại: " + me.getEmail());
      }
    } catch (error) {
      Logger.log("Lỗi khi cập nhật quyền: " + error);
    }

    // Kích hoạt sheet
    newSheet.activate();

    // Ẩn tất cả các sheet khác trừ sheet hiện tại và Sheet1, Sheet2
    var allSheets = ss.getSheets();
    for (var i = 0; i < allSheets.length; i++) {
      var currentSheet = allSheets[i];
      var currentSheetName = currentSheet.getName();

      // Không ẩn sheet hiện tại, Sheet1 và Sheet2
      if (currentSheetName !== sheetName &&
          currentSheetName !== "Sheet1" &&
          currentSheetName !== "Sheet2") {

        // Kiểm tra xem người dùng có phải admin không
        var isAdmin = kiemTraQuyenAdmin(email);

        // Nếu không phải admin, ẩn sheet khác
        if (!isAdmin) {
          currentSheet.hideSheet();
        }
      }
    }
  }
}
}

// Hàm tạo form nhập kế hoạch công việc
function showKeHoachCongViecForm() {
var html = HtmlService.createHtmlOutputFromFile('FormKeHoachCongViec')
  .setWidth(600)
  .setHeight(400)
  .setTitle('Nhập Kế Hoạch Công Việc');
SpreadsheetApp.getUi().showModalDialog(html, 'Nhập Kế Hoạch Công Việc');
}

// Hàm tạo tên sheet từ họ tên theo định dạng mới (X_Y_Z_Ten)
function taoTenSheetTuHoTen(hoTen) {
  // Loại bỏ dấu tiếng Việt
  var hoTenKhongDau = loaiDauTiengViet(hoTen);

  // Tách tên thành các từ
  var cacTu = hoTenKhongDau.split(' ');

  // Lấy chữ cái đầu tiên của mỗi từ
  var chuCaiDau = [];
  var phanTen = "";

  // Nếu có ít nhất 2 từ, lấy từ cuối cùng làm phần tên
  if (cacTu.length >= 2) {
    phanTen = cacTu[cacTu.length - 1];

    // Lấy chữ cái đầu tiên của các từ còn lại
    for (var i = 0; i < cacTu.length - 1; i++) {
      if (cacTu[i].length > 0) {
        chuCaiDau.push(cacTu[i].charAt(0).toUpperCase());
      }
    }
  } else {
    // Nếu chỉ có 1 từ, lấy từ đó làm phần tên
    phanTen = cacTu[0];
  }

  // Tạo tên sheet theo định dạng X_Y_Z_Ten
  var tenSheet = chuCaiDau.join('_');
  if (tenSheet.length > 0) {
    tenSheet += '_' + phanTen;
  } else {
    tenSheet = phanTen;
  }

  return tenSheet;
}

// Hàm loại bỏ dấu tiếng Việt
function loaiDauTiengViet(str) {
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
  str = str.replace(/đ/g, "d");
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
  str = str.replace(/Đ/g, "D");
  return str;
}

// Hàm thêm mục kế hoạch công việc mới
function themKeHoachCongViec(formData) {
var userInfo = getCurrentUserInfo();
var hoTen = userInfo.hoTen;

// Tạo tên sheet từ họ tên theo định dạng mới (X_Y_Z_Ten)
var sheetName = taoTenSheetTuHoTen(hoTen);

var ss = SpreadsheetApp.getActiveSpreadsheet();
var sheet = ss.getSheetByName(sheetName);

if (!sheet) {
  return "Không tìm thấy sheet kế hoạch công việc của bạn!";
}

// Tìm dòng trống đầu tiên
var lastRow = 4; // Bắt đầu từ dòng 4 (sau header và mô tả)
while (sheet.getRange("B" + lastRow).getValue() !== "") {
  lastRow++;
}

// Thêm dữ liệu vào sheet
sheet.getRange("A" + lastRow).setValue(formData.ngayThang);
sheet.getRange("B" + lastRow).setValue(formData.hangMuc);
sheet.getRange("C" + lastRow).setValue(formData.noiDung);

return "Đã thêm kế hoạch công việc thành công!";
}

