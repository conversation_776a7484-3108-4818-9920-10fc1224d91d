<!DOCTYPE html>
<html>
  <head>
    <base target="_top">
    <style>
      /* CSS Chung */
      body {
        font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
        line-height: 1.6;
        background-color: #f5f5f5;
        color: #333;
        padding: 20px;
        margin: 0;
      }

      /* Container chính */
      .container {
        max-width: 500px;
        margin: 0 auto;
        background: #fff;
        padding: 25px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      /* Heading */
      h3 {
        color: #2c3e50;
        text-align: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid #3498db;
      }

      /* Form */
      .form-group {
        margin-bottom: 18px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #2c3e50;
      }

      .form-control {
        width: 100%;
        padding: 10px;
        font-size: 14px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
        transition: border-color 0.3s;
      }

      .form-control:focus {
        border-color: #3498db;
        outline: none;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      }

      .form-control[readonly] {
        background-color: #f9f9f9;
        cursor: not-allowed;
      }

      /* Select box */
      select.form-control {
        height: 40px;
        background-color: white;
      }

      /* Project info */
      .project-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        border-left: 4px solid #3498db;
        margin-bottom: 20px;
      }

      .project-info p {
        margin: 5px 0;
      }

      .project-info strong {
        color: #2c3e50;
      }

      /* User info */
      .user-info {
        display: flex;
        align-items: center;
        margin-bottom: 25px;
        padding: 10px;
        background-color: #e8f4fc;
        border-radius: 6px;
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 15px;
        background-color: #3498db;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
      }

      .user-details {
        flex-grow: 1;
      }

      .user-name {
        font-weight: 500;
        color: #2c3e50;
      }

      .user-email {
        font-size: 12px;
        color: #7f8c8d;
      }

      .user-role {
        font-size: 12px;
        color: #7f8c8d;
      }

      /* Buttons */
      .btn-container {
        margin-top: 25px;
        display: flex;
        justify-content: space-between;
      }

      .btn {
        padding: 10px 18px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 15px;
        border: none;
        transition: all 0.3s;
      }

      .btn-primary {
        background-color: #3498db;
        color: white;
        flex-grow: 2;
      }

      .btn-primary:hover {
        background-color: #2980b9;
      }

      .btn-secondary {
        background-color: #95a5a6;
        color: white;
        margin-right: 10px;
        flex-grow: 1;
      }

      .btn-secondary:hover {
        background-color: #7f8c8d;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h3>Duyệt Dự Án</h3>

      <div class="user-info">
        <div class="user-avatar" id="userInitials">?</div>
        <div class="user-details">
          <div class="user-name" id="userName">Đang tải...</div>
          <div class="user-email" id="userEmail">Đang tải...</div>
          <div class="user-role">Người duyệt</div>
        </div>
      </div>

      <div class="form-group">
        <label for="maDuAn">Chọn dự án:</label>
        <select class="form-control" id="maDuAn" onchange="loadProjectDetails()">
          <option value="">-- Chọn dự án cần duyệt --</option>
        </select>
      </div>

      <div id="projectDetails" style="display:none;" class="project-info">
        <p><strong>Tên dự án:</strong> <span id="tenDuAn"></span></p>
        <p><strong>Loại dự án:</strong> <span id="loaiDuAn"></span></p>
        <p><strong>Mục đích:</strong> <span id="mucDich"></span></p>
        <p><strong>Lợi ích:</strong> <span id="loiIch"></span></p>
        <p><strong>Người tạo:</strong> <span id="nguoiTao"></span></p>
      </div>

      <div id="approvalForm" style="display:none;">
        <div class="form-group">
          <label for="ngayDuyet">Ngày duyệt:</label>
          <input type="date" class="form-control" id="ngayDuyet" required>
        </div>

        <div class="form-group">
          <label for="lanDuyet">Lần duyệt:</label>
          <select class="form-control" id="lanDuyet" onchange="checkApprovalLevel()">
            <option value="1">Duyệt lần 1</option>
            <option value="2">Duyệt lần 2</option>
          </select>
        </div>

        <div class="btn-container">
          <button type="button" class="btn btn-secondary" onclick="google.script.host.close()">Hủy</button>
          <button type="button" class="btn btn-primary" onclick="approveProject()">Duyệt dự án</button>
        </div>
      </div>
    </div>

    <script>
      // Biến lưu thông tin người dùng
      var currentUserEmail = '';
      var currentUserName = '';

      // Khi trang được load
      document.addEventListener('DOMContentLoaded', function() {
        // Thiết lập ngày mặc định là ngày hôm nay
        document.getElementById('ngayDuyet').valueAsDate = new Date();

        // Lấy thông tin người dùng đang đăng nhập
        google.script.run.withSuccessHandler(setUserInfo).getCurrentUserInfo();

        // Lấy danh sách dự án
        google.script.run.withSuccessHandler(populateProjects).getDanhSachMaDuAn();
      });

      // Thiết lập thông tin người dùng
      function setUserInfo(userInfo) {
        currentUserEmail = userInfo.email;
        currentUserName = userInfo.hoTen || userInfo.email.split('@')[0];

        document.getElementById('userName').textContent = currentUserName;
        document.getElementById('userEmail').textContent = currentUserEmail;

        // Tạo chữ cái đầu cho avatar
        var initials = '';
        if (currentUserName) {
          var nameParts = currentUserName.split(' ');
          if (nameParts.length > 1) {
            initials = nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0);
          } else {
            initials = nameParts[0].charAt(0);
          }
        } else {
          initials = userInfo.email.charAt(0).toUpperCase();
        }
        document.getElementById('userInitials').textContent = initials.toUpperCase();
      }

      // Điền danh sách dự án vào dropdown
      function populateProjects(projects) {
        var select = document.getElementById('maDuAn');

        projects.forEach(function(project) {
          var option = document.createElement('option');
          option.value = project.ma;
          option.text = project.ma + ' - ' + project.ten;
          select.appendChild(option);
        });
      }

      // Load thông tin chi tiết dự án khi chọn
      function loadProjectDetails() {
        var maDuAn = document.getElementById('maDuAn').value;

        if (maDuAn) {
          google.script.run.withSuccessHandler(showProjectDetails).timDuAnTheoMa(maDuAn);
        } else {
          document.getElementById('projectDetails').style.display = 'none';
          document.getElementById('approvalForm').style.display = 'none';
        }
      }

      // Hiển thị thông tin chi tiết dự án
      function showProjectDetails(project) {
        if (project) {
          document.getElementById('tenDuAn').textContent = project.data.tenDuAn;
          document.getElementById('loaiDuAn').textContent = project.data.loaiDuAn;
          document.getElementById('mucDich').textContent = project.data.mucDich;
          document.getElementById('loiIch').textContent = project.data.loiIch;
          document.getElementById('nguoiTao').textContent = project.data.nguoiTao;

          // Kiểm tra trạng thái duyệt
          if (project.data.nguoiDuyet && project.data.nguoiDuyet.trim() !== '') {
            // Nếu đã có người duyệt lần 1
            var lanDuyetSelect = document.getElementById('lanDuyet');
            lanDuyetSelect.value = "2";
            lanDuyetSelect.disabled = true; // Khóa không cho chọn lần duyệt 1

            if (project.data.nguoiDuyetLan2 && project.data.nguoiDuyetLan2.trim() !== '') {
              // Nếu đã duyệt cả 2 lần
              alert('Dự án này đã được duyệt đủ 2 lần:\nLần 1 bởi ' + project.data.nguoiDuyet +
                    ' vào ngày ' + project.data.ngayDuyet +
                    '\nLần 2 bởi ' + project.data.nguoiDuyetLan2 +
                    ' vào ngày ' + project.data.ngayDuyetLan2);
              document.getElementById('projectDetails').style.display = 'block';
              document.getElementById('approvalForm').style.display = 'none';
            } else {
              document.getElementById('projectDetails').style.display = 'block';
              document.getElementById('approvalForm').style.display = 'block';

              // Hiển thị thông báo về việc dự án đã được duyệt lần 1
              var approveButton = document.querySelector('.btn-primary');
              approveButton.textContent = "Duyệt lần 2";
            }
          } else {
            document.getElementById('projectDetails').style.display = 'block';
            document.getElementById('approvalForm').style.display = 'block';
            document.getElementById('lanDuyet').disabled = false;
            document.getElementById('lanDuyet').value = "1";

            // Cập nhật nút duyệt
            var approveButton = document.querySelector('.btn-primary');
            approveButton.textContent = "Duyệt lần 1";
          }
        }
      }

      // Kiểm tra và cập nhật giao diện theo lần duyệt
      function checkApprovalLevel() {
        var lanDuyet = document.getElementById('lanDuyet').value;
        var approveButton = document.querySelector('.btn-primary');

        if (lanDuyet === "2") {
          approveButton.textContent = "Duyệt lần 2";
        } else {
          approveButton.textContent = "Duyệt lần 1";
        }
      }

      // Duyệt dự án
      function approveProject() {
        var maDuAn = document.getElementById('maDuAn').value;
        var ngayDuyet = document.getElementById('ngayDuyet').value;
        var lanDuyet = parseInt(document.getElementById('lanDuyet').value);

        if (!maDuAn) {
          alert("Vui lòng chọn một dự án để duyệt!");
          return;
        }

        if (!ngayDuyet) {
          alert("Vui lòng chọn ngày duyệt!");
          return;
        }

        // Kiểm tra nếu đang duyệt lần 2 mà dự án chưa được duyệt lần 1
        if (lanDuyet === 2) {
          var project = document.getElementById('tenDuAn').textContent;
          if (!project) {
            alert("Vui lòng chọn dự án trước khi duyệt!");
            return;
          }
        }

        // Gọi hàm duyệt dự án từ Google Script
        var approvalData = {
          maDuAn: maDuAn,
          ngayDuyet: ngayDuyet,
          lanDuyet: lanDuyet
        };

        google.script.run
          .withSuccessHandler(function(msg) {
            alert(msg);
            google.script.host.close();
          })
          .withFailureHandler(function(error) {
            alert("Lỗi: " + error);
          })
          .duyetDuAn(approvalData);
      }
    </script>
  </body>
</html>